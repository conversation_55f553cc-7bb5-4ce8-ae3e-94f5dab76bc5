import { Editor, MarkdownView, Notice, Plugin, normalizePath } from 'obsidian'
import { sql } from 'drizzle-orm'

import { ApplyView } from './ApplyView'
import { ChatView } from './ChatView'
import { ChatProps } from './components/chat-view/Chat'
import { InstallerUpdateRequiredModal } from './components/modals/InstallerUpdateRequiredModal'
import { APPLY_VIEW_TYPE, CHAT_VIEW_TYPE } from './constants'
import { McpManager } from './core/mcp/mcpManager'
import { RAGEngine } from './core/rag/ragEngine'
import { DatabaseManager } from './database/DatabaseManager'
import { PGLiteAbortedException } from './database/exception'
import { migrateToJsonDatabase } from './database/json/migrateToJsonDatabase'
import {
  SmartComposerSettings,
  smartComposerSettingsSchema,
} from './settings/schema/setting.types'
import { parseSmartComposerSettings } from './settings/schema/settings'
import { SmartComposerSettingTab } from './settings/SettingTab'
import { getMentionableBlockData } from './utils/obsidian'

export default class SmartComposerPlugin extends Plugin {
  settings: SmartComposerSettings
  initialChatProps?: ChatProps // TODO: change this to use view state like ApplyView
  settingsChangeListeners: ((newSettings: SmartComposerSettings) => void)[] = []
  mcpManager: McpManager | null = null
  dbManager: DatabaseManager | null = null
  ragEngine: RAGEngine | null = null
  private dbManagerInitPromise: Promise<DatabaseManager> | null = null
  private ragEngineInitPromise: Promise<RAGEngine> | null = null
  private timeoutIds: ReturnType<typeof setTimeout>[] = [] // Use ReturnType instead of number

  async onload() {
    await this.loadSettings()

    this.registerView(CHAT_VIEW_TYPE, (leaf) => new ChatView(leaf, this))
    this.registerView(APPLY_VIEW_TYPE, (leaf) => new ApplyView(leaf))

    // This creates an icon in the left ribbon.
    this.addRibbonIcon('wand-sparkles', 'Open smart composer', () =>
      this.openChatView(),
    )

    // This adds a simple command that can be triggered anywhere
    this.addCommand({
      id: 'open-new-chat',
      name: 'Open chat',
      callback: () => this.openChatView(true),
    })

    this.addCommand({
      id: 'add-selection-to-chat',
      name: 'Add selection to chat',
      editorCallback: (editor: Editor, view: MarkdownView) => {
        this.addSelectionToChat(editor, view)
      },
    })

    this.addCommand({
      id: 'rebuild-vault-index',
      name: 'Rebuild entire vault index',
      callback: async () => {
        const notice = new Notice('Rebuilding vault index...', 0)
        try {
          const ragEngine = await this.getRAGEngine()
          await ragEngine.updateVaultIndex(
            { reindexAll: true },
            (queryProgress) => {
              if (queryProgress.type === 'indexing') {
                const { completedChunks, totalChunks } =
                  queryProgress.indexProgress
                notice.setMessage(
                  `Indexing chunks: ${completedChunks} / ${totalChunks}${
                    queryProgress.indexProgress.waitingForRateLimit
                      ? '\n(waiting for rate limit to reset)'
                      : ''
                  }`,
                )
              }
            },
          )
          notice.setMessage('Rebuilding vault index complete')
        } catch (error) {
          console.error(error)
          notice.setMessage('Rebuilding vault index failed')
        } finally {
          this.registerTimeout(() => {
            notice.hide()
          }, 1000)
        }
      },
    })

    this.addCommand({
      id: 'update-vault-index',
      name: 'Update index for modified files',
      callback: async () => {
        const notice = new Notice('Updating vault index...', 0)
        try {
          const ragEngine = await this.getRAGEngine()
          await ragEngine.updateVaultIndex(
            { reindexAll: false },
            (queryProgress) => {
              if (queryProgress.type === 'indexing') {
                const { completedChunks, totalChunks } =
                  queryProgress.indexProgress
                notice.setMessage(
                  `Indexing chunks: ${completedChunks} / ${totalChunks}${
                    queryProgress.indexProgress.waitingForRateLimit
                      ? '\n(waiting for rate limit to reset)'
                      : ''
                  }`,
                )
              }
            },
          )
          notice.setMessage('Vault index updated')
        } catch (error) {
          console.error(error)
          notice.setMessage('Vault index update failed')
        } finally {
          this.registerTimeout(() => {
            notice.hide()
          }, 1000)
        }
      },
    })

    this.addCommand({
      id: 'diagnose-rag-system',
      name: 'Diagnose RAG System',
      callback: async () => {
        const notice = new Notice('Diagnosing RAG system...', 0)
        try {
          console.log('=== RAG System Diagnosis ===')

          // Check database manager
          console.log('1. Checking database manager...')
          const dbManager = await this.getDbManager()
          console.log('Database manager:', dbManager ? 'OK' : 'FAILED')

          // Check RAG engine
          console.log('2. Checking RAG engine...')
          const ragEngine = await this.getRAGEngine()
          console.log('RAG engine:', ragEngine ? 'OK' : 'FAILED')

          // Check settings
          console.log('3. Checking settings...')
          console.log('Settings:', this.settings)
          console.log('Embedding model ID:', this.settings.embeddingModelId)
          console.log('RAG options:', this.settings.ragOptions)

          // Check embedding model
          console.log('4. Checking embedding model...')
          const embeddingModel = this.settings.embeddingModels.find(
            m => m.id === this.settings.embeddingModelId
          )
          console.log('Embedding model config:', embeddingModel)

          // Check provider
          if (embeddingModel) {
            const provider = this.settings.providers.find(
              p => p.id === embeddingModel.providerId
            )
            console.log('Provider config:', provider)
          }

          // Test RAG query
          console.log('5. Testing RAG query...')
          const testResults = await ragEngine.processQuery({
            query: 'test query',
            onQueryProgressChange: (progress) => {
              console.log('Query progress:', progress)
            }
          })
          console.log('Test query results:', testResults)

          notice.setMessage('RAG diagnosis complete - check console')
        } catch (error) {
          console.error('RAG diagnosis failed:', error)
          notice.setMessage(`RAG diagnosis failed: ${error.message}`)
        } finally {
          this.registerTimeout(() => {
            notice.hide()
          }, 3000)
        }
      },
    })

    this.addCommand({
      id: 'reset-rag-database',
      name: 'Reset RAG Database (Nuclear Option)',
      callback: async () => {
        const notice = new Notice('Resetting RAG database...', 0)
        try {
          console.log('=== Resetting RAG Database ===')

          // Clean up existing instances
          this.ragEngine?.cleanup()
          this.ragEngine = null
          this.ragEngineInitPromise = null

          await this.dbManager?.cleanup()
          this.dbManager = null
          this.dbManagerInitPromise = null

          // Delete database file
          const dbPath = normalizePath('.obsidian/plugins/obsidian-smart-composer/database.db')
          if (await this.app.vault.adapter.exists(dbPath)) {
            await this.app.vault.adapter.remove(dbPath)
            console.log('Database file deleted')
          }

          // Wait a moment for cleanup
          await new Promise(resolve => setTimeout(resolve, 1000))

          // Reinitialize
          console.log('Reinitializing database...')
          const newDbManager = await this.getDbManager()
          console.log('Database reinitialized:', newDbManager ? 'OK' : 'FAILED')

          const newRagEngine = await this.getRAGEngine()
          console.log('RAG engine reinitialized:', newRagEngine ? 'OK' : 'FAILED')

          // Try to rebuild index
          try {
            console.log('Rebuilding index...')
            await newRagEngine.updateVaultIndex({ reindexAll: true })
            notice.setMessage('RAG database reset complete!')
          } catch (indexError) {
            console.warn('Index rebuild failed, but database reset successful:', indexError)
            notice.setMessage('Database reset complete, but indexing failed (vector extension issue)')
          }

          console.log('=== RAG Database Reset Complete ===')
        } catch (error) {
          console.error('RAG database reset failed:', error)
          notice.setMessage(`RAG database reset failed: ${error.message}`)
        } finally {
          this.registerTimeout(() => {
            notice.hide()
          }, 5000)
        }
      },
    })

    this.addCommand({
      id: 'fix-vector-extension-issue',
      name: 'Fix Vector Extension Issue',
      callback: async () => {
        const notice = new Notice('Fixing vector extension issue...', 0)
        try {
          console.log('=== Fixing Vector Extension Issue ===')

          // Step 1: Clean up everything
          console.log('Step 1: Cleaning up existing instances...')
          this.ragEngine?.cleanup()
          this.ragEngine = null
          this.ragEngineInitPromise = null

          await this.dbManager?.cleanup()
          this.dbManager = null
          this.dbManagerInitPromise = null

          // Step 2: Delete database file to force recreation
          console.log('Step 2: Deleting corrupted database...')
          const dbPath = normalizePath('.obsidian/plugins/obsidian-smart-composer/database.db')
          if (await this.app.vault.adapter.exists(dbPath)) {
            await this.app.vault.adapter.remove(dbPath)
            console.log('Database file deleted')
          }

          // Step 3: Wait and reinitialize
          console.log('Step 3: Waiting for cleanup...')
          await new Promise(resolve => setTimeout(resolve, 2000))

          console.log('Step 4: Reinitializing with fallback...')
          const newDbManager = await this.getDbManager()
          console.log('Database manager:', newDbManager ? 'OK' : 'FAILED')

          // Step 5: Test basic functionality
          console.log('Step 5: Testing basic functionality...')
          const db = newDbManager.getDb()
          if (db) {
            // Test basic query
            await db.execute(sql`SELECT 1 as test`)
            console.log('Basic database functionality: OK')
          }

          notice.setMessage('Vector extension issue fixed! RAG may work without vector search.')
          console.log('=== Vector Extension Issue Fix Complete ===')

        } catch (error) {
          console.error('Failed to fix vector extension issue:', error)
          notice.setMessage(`Fix failed: ${error.message}`)
        } finally {
          this.registerTimeout(() => {
            notice.hide()
          }, 5000)
        }
      },
    })

    // This adds a settings tab so the user can configure various aspects of the plugin
    this.addSettingTab(new SmartComposerSettingTab(this.app, this))

    void this.migrateToJsonStorage()
  }

  onunload() {
    // clear all timers
    this.timeoutIds.forEach((id) => clearTimeout(id))
    this.timeoutIds = []

    // RagEngine cleanup
    this.ragEngine?.cleanup()
    this.ragEngine = null

    // Promise cleanup
    this.dbManagerInitPromise = null
    this.ragEngineInitPromise = null

    // DatabaseManager cleanup
    this.dbManager?.cleanup()
    this.dbManager = null

    // McpManager cleanup
    this.mcpManager?.cleanup()
    this.mcpManager = null
  }

  async loadSettings() {
    this.settings = parseSmartComposerSettings(await this.loadData())
    await this.saveData(this.settings) // Save updated settings
  }

  async setSettings(newSettings: SmartComposerSettings) {
    const validationResult = smartComposerSettingsSchema.safeParse(newSettings)

    if (!validationResult.success) {
      new Notice(`Invalid settings:
${validationResult.error.issues.map((v) => v.message).join('\n')}`)
      return
    }

    this.settings = newSettings
    await this.saveData(newSettings)
    this.ragEngine?.setSettings(newSettings)
    this.settingsChangeListeners.forEach((listener) => listener(newSettings))
  }

  addSettingsChangeListener(
    listener: (newSettings: SmartComposerSettings) => void,
  ) {
    this.settingsChangeListeners.push(listener)
    return () => {
      this.settingsChangeListeners = this.settingsChangeListeners.filter(
        (l) => l !== listener,
      )
    }
  }

  async openChatView(openNewChat = false) {
    const view = this.app.workspace.getActiveViewOfType(MarkdownView)
    const editor = view?.editor
    if (!view || !editor) {
      this.activateChatView(undefined, openNewChat)
      return
    }
    const selectedBlockData = await getMentionableBlockData(editor, view)
    this.activateChatView(
      {
        selectedBlock: selectedBlockData ?? undefined,
      },
      openNewChat,
    )
  }

  async activateChatView(chatProps?: ChatProps, openNewChat = false) {
    // chatProps is consumed in ChatView.tsx
    this.initialChatProps = chatProps

    const leaf = this.app.workspace.getLeavesOfType(CHAT_VIEW_TYPE)[0]

    await (leaf ?? this.app.workspace.getRightLeaf(false))?.setViewState({
      type: CHAT_VIEW_TYPE,
      active: true,
    })

    if (openNewChat && leaf && leaf.view instanceof ChatView) {
      leaf.view.openNewChat(chatProps?.selectedBlock)
    }

    this.app.workspace.revealLeaf(
      this.app.workspace.getLeavesOfType(CHAT_VIEW_TYPE)[0],
    )
  }

  async addSelectionToChat(editor: Editor, view: MarkdownView) {
    const data = await getMentionableBlockData(editor, view)
    if (!data) return

    const leaves = this.app.workspace.getLeavesOfType(CHAT_VIEW_TYPE)
    if (leaves.length === 0 || !(leaves[0].view instanceof ChatView)) {
      await this.activateChatView({
        selectedBlock: data,
      })
      return
    }

    // bring leaf to foreground (uncollapse sidebar if it's collapsed)
    await this.app.workspace.revealLeaf(leaves[0])

    const chatView = leaves[0].view
    chatView.addSelectionToChat(data)
    chatView.focusMessage()
  }

  async getDbManager(): Promise<DatabaseManager> {
    if (this.dbManager) {
      return this.dbManager
    }

    if (!this.dbManagerInitPromise) {
      this.dbManagerInitPromise = (async () => {
        try {
          this.dbManager = await DatabaseManager.create(this.app)
          return this.dbManager
        } catch (error) {
          this.dbManagerInitPromise = null
          if (error instanceof PGLiteAbortedException) {
            new InstallerUpdateRequiredModal(this.app).open()
          }
          throw error
        }
      })()
    }

    // if initialization is running, wait for it to complete instead of creating a new initialization promise
    return this.dbManagerInitPromise
  }

  async getRAGEngine(): Promise<RAGEngine> {
    if (this.ragEngine) {
      return this.ragEngine
    }

    if (!this.ragEngineInitPromise) {
      this.ragEngineInitPromise = (async () => {
        try {
          const dbManager = await this.getDbManager()
          this.ragEngine = new RAGEngine(
            this.app,
            this.settings,
            dbManager.getVectorManager(),
          )
          return this.ragEngine
        } catch (error) {
          this.ragEngineInitPromise = null
          throw error
        }
      })()
    }

    return this.ragEngineInitPromise
  }

  async getMcpManager(): Promise<McpManager> {
    if (this.mcpManager) {
      return this.mcpManager
    }

    try {
      this.mcpManager = new McpManager({
        settings: this.settings,
        registerSettingsListener: (
          listener: (settings: SmartComposerSettings) => void,
        ) => this.addSettingsChangeListener(listener),
      })
      await this.mcpManager.initialize()
      return this.mcpManager
    } catch (error) {
      this.mcpManager = null
      throw error
    }
  }

  private registerTimeout(callback: () => void, timeout: number): void {
    const timeoutId = setTimeout(callback, timeout)
    this.timeoutIds.push(timeoutId)
  }

  private async migrateToJsonStorage() {
    try {
      const dbManager = await this.getDbManager()
      await migrateToJsonDatabase(this.app, dbManager, async () => {
        await this.reloadChatView()
        console.log('Migration to JSON storage completed successfully')
      })
    } catch (error) {
      console.warn('Failed to migrate to JSON storage (this is expected if PGLite database was never created):', error)
      // This is not a critical error - the plugin can work without migration
      // if the user never used the old PGLite-based features
    }
  }

  private async reloadChatView() {
    const leaves = this.app.workspace.getLeavesOfType(CHAT_VIEW_TYPE)
    if (leaves.length === 0 || !(leaves[0].view instanceof ChatView)) {
      return
    }
    new Notice('Reloading "smart-composer" due to migration', 1000)
    leaves[0].detach()
    await this.activateChatView()
  }
}
